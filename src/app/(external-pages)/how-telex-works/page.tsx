"use server";
import React from "react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const metadata: Metadata = {
  title: "How Telex Works - Telex",
  description:
    "Learn how Telex works in 3 easy steps: Set up your account, create and manage channels, and monitor and collaborate in real-time with AI agents.",
  icons: {
    icon: "/TelexIcon.svg",
  },
};

function HowTelexWorks() {
  return (
    <>
      <main className="bg-white min-h-screen">
        <div className="flex flex-col md:gap-2 md:items-center pt-[100px] mb-3.5 md:mb-20 ml-6 md:ml-0">
          <h1 className="font-semibold text-[28px] md:text-5xl">
            How Telex works
          </h1>
          <p className="text-base">3 easy steps</p>
        </div>
        <div className="flex flex-col md:flex-row gap-10 bg-[#7141F8] p-6 md:px-16 md:py-24 justify-between rounded">
          <div className="flex flex-col justify-center max-w-[568px]">
            <p className="text-[#D0D5DD] text-2xl md:text-[28px]">01</p>
            <h2 className="text-2xl md:text-4xl text-white mb-6 font-semibold">
              Set up your account
            </h2>
            <p className="text-base text-white">
              Sign up with your email and password to create your Telex account.
              Then, verify your email and log in with your credentials to access
              the dashboard.
            </p>
            <Link
              href="auth/sign-up"
              className="mr-auto text-[#7141F8] text-sm mt-10 flex bg-white py-3 px-6 rounded gap-2.5 items-center"
            >
              Sign Up now{" "}
              <Image
                src="/arrow-right.svg"
                alt="icon"
                width={16}
                height={16}
              />{" "}
            </Link>
          </div>
          <div className="border-4 md:border-8 border-[#C6B3FC] rounded">
            <Image
              src="/images/Integration-View.jpg"
              alt="Step 1"
              width={576}
              height={407}
              className="rounded"
            />
          </div>
        </div>
        <div className="flex flex-col-reverse md:flex-row gap-10  bg-white p-6 md:px-16 md:py-24 justify-between">
          <div className="border-4 md:border-8 border-[#C6B3FC] rounded">
            <Image
              src="/images/Create channel.jpg"
              alt="Step 1"
              width={576}
              height={407}
              className="rounded"
            />
          </div>
          <div className="flex flex-col justify-center max-w-[568px]">
            <p className="text-[#D0D5DD] text-2xl md:text-[28px]">02</p>
            <h2 className="text-2xl md:text-4xl text-black mb-6 font-semibold">
              Create and Manage Channels
            </h2>
            <p className="text-base text-black mb-5">
              Create Channels: Go to the events section on the dashboard to set
              up channels for different event types in the dashboard. Name each
              channel (e.g Customer Sign-Ups, Build-feed) and specify the
              notifications it will handle for efficient categorization.
            </p>
            <p className="text-base text-black">
              Manage Channels: Customize channel settings for easy access and
              actionability. Adjust notification preferences, user permissions,
              and integrations, and rename or delete channels as needed to stay
              organized.
            </p>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-10 bg-white p-6 md:px-16 md:py-24 justify-between">
          <div className="flex flex-col justify-center max-w-[568px]">
            <p className="text-[#D0D5DD] text-2xl md:text-[28px]">03</p>
            <h2 className="text-2xl md:text-4xl text-black mb-6 font-semibold">
              Monitor and Collaborate in real-time
            </h2>
            <p className="text-base text-black mb-5">
              Monitor Events: View all app-related events in real-time from the
              dashboard. Filter and prioritize critical events and receive
              immediate alerts via email, SMS, and other devices.
            </p>
            <p className="text-base text-black">
              Collaborate: Improve team discussions within channels with the
              in-app chat and commenting. Assign tasks directly related to
              events, manage user settings, and ensure security with robust
              authentication measures.
            </p>
          </div>
          <div className="border-4 md:border-8 border-[#C6B3FC] rounded">
            <Image
              src="/images/Search.jpg"
              alt="Step 1"
              width={576}
              height={407}
              className="rounded"
            />
          </div>
        </div>
      </main>
    </>
  );
}

export default HowTelexWorks;
