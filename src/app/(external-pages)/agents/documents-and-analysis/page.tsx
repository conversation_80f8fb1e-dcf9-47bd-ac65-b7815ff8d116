import React from "react";
import { Metadata } from "next";
import CategoryPageClient from "../components/category-page-client";

// Metadata for SEO
export const metadata: Metadata = {
  title: "Documents and Analysis AI Agents | Telex",
  description:
    "Transform your document processing and analysis with intelligent AI agents. Automate document review, data extraction, content analysis, and report generation.",
  keywords:
    "document analysis AI, document processing, data extraction, content analysis, report automation, document management",
  openGraph: {
    title: "Documents and Analysis AI Agents | Telex",
    description:
      "Transform your document processing and analysis with intelligent AI agents. Automate document review and data extraction.",
    type: "website",
  },
};

export default function DocumentsAndAnalysisAgents() {
  return (
    <div className="bg-white relative">
      <CategoryPageClient 
        category="Documents and Analysis" 
        categoryDisplayName="Documents and Analysis"
      />
    </div>
  );
}
