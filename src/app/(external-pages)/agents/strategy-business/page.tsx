import React from "react";
import { Metadata } from "next";
import CategoryPageClient from "../components/category-page-client";

// Metadata for SEO
export const metadata: Metadata = {
  title: "Strategy Business AI Agents | Telex",
  description:
    "Enhance your business strategy and decision-making with specialized AI agents. Get insights on market analysis, competitive intelligence, business planning, and strategic consulting.",
  keywords:
    "business strategy AI, strategic planning, market analysis, competitive intelligence, business consulting, decision support",
  openGraph: {
    title: "Strategy Business AI Agents | Telex",
    description:
      "Enhance your business strategy and decision-making with specialized AI agents. Get insights on market analysis and strategic planning.",
    type: "website",
  },
};

export default function StrategyBusinessAgents() {
  return (
    <div className="bg-white relative">
      <CategoryPageClient 
        category="Strategy Business" 
        categoryDisplayName="Strategy Business"
      />
    </div>
  );
}
