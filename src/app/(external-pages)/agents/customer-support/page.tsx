import React from "react";
import { getCategoryConfig } from "../lib/categories";
import CategoryAgentsPageClient from "../components/category-agents-page-client";

// Get category configuration
const categoryConfig = getCategoryConfig("customer-support");

// Export metadata
export const metadata = categoryConfig?.metadata || {};

export default function CustomerSupportAgents() {
  if (!categoryConfig) {
    return <div>Category not found</div>;
  }

  return (
    <div className="bg-white relative">
      <CategoryAgentsPageClient categoryName={categoryConfig.name} />
    </div>
  );
}
