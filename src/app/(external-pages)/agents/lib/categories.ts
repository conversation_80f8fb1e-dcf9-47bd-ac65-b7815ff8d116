import { Metadata } from "next";

export interface CategoryConfig {
  slug: string;
  name: string;
  displayName: string;
  metadata: Metadata;
}

export const categoryConfigs: Record<string, CategoryConfig> = {
  "customer-support": {
    slug: "customer-support",
    name: "Customer Support",
    displayName: "Customer Support",
    metadata: {
      title: "Customer Support AI Agents | Telex",
      description:
        "Discover powerful AI agents specialized in customer support. Automate customer service, improve response times, and enhance customer satisfaction with our intelligent support agents.",
      keywords:
        "customer support AI, support automation, customer service agents, AI chatbots, help desk automation",
      openGraph: {
        title: "Customer Support AI Agents | Telex",
        description:
          "Discover powerful AI agents specialized in customer support. Automate customer service and enhance customer satisfaction.",
        type: "website",
      },
    },
  },
  "sales-and-marketing": {
    slug: "sales-and-marketing",
    name: "Sales and Marketing",
    displayName: "Sales and Marketing",
    metadata: {
      title: "Sales and Marketing AI Agents | Telex",
      description:
        "Boost your sales and marketing efforts with specialized AI agents. Automate lead generation, customer outreach, campaign management, and sales analytics.",
      keywords:
        "sales AI agents, marketing automation, lead generation, sales analytics, campaign management, CRM automation",
      openGraph: {
        title: "Sales and Marketing AI Agents | Telex",
        description:
          "Boost your sales and marketing efforts with specialized AI agents. Automate lead generation and campaign management.",
        type: "website",
      },
    },
  },
  "engineering-devops": {
    slug: "engineering-devops",
    name: "Engineering DevOps",
    displayName: "Engineering DevOps",
    metadata: {
      title: "Engineering DevOps AI Agents | Telex",
      description:
        "Streamline your development and operations with specialized AI agents. Automate deployments, monitor infrastructure, manage CI/CD pipelines, and optimize system performance.",
      keywords:
        "DevOps AI agents, infrastructure monitoring, CI/CD automation, deployment automation, system monitoring, engineering tools",
      openGraph: {
        title: "Engineering DevOps AI Agents | Telex",
        description:
          "Streamline your development and operations with specialized AI agents. Automate deployments and monitor infrastructure.",
        type: "website",
      },
    },
  },
  "documents-and-analysis": {
    slug: "documents-and-analysis",
    name: "Documents and Analysis",
    displayName: "Documents and Analysis",
    metadata: {
      title: "Documents and Analysis AI Agents | Telex",
      description:
        "Transform your document processing and analysis with intelligent AI agents. Automate document review, data extraction, content analysis, and report generation.",
      keywords:
        "document analysis AI, document processing, data extraction, content analysis, report automation, document management",
      openGraph: {
        title: "Documents and Analysis AI Agents | Telex",
        description:
          "Transform your document processing and analysis with intelligent AI agents. Automate document review and data extraction.",
        type: "website",
      },
    },
  },
  "strategy-business": {
    slug: "strategy-business",
    name: "Strategy Business",
    displayName: "Strategy Business",
    metadata: {
      title: "Strategy Business AI Agents | Telex",
      description:
        "Enhance your business strategy and decision-making with specialized AI agents. Get insights on market analysis, competitive intelligence, business planning, and strategic consulting.",
      keywords:
        "business strategy AI, strategic planning, market analysis, competitive intelligence, business consulting, decision support",
      openGraph: {
        title: "Strategy Business AI Agents | Telex",
        description:
          "Enhance your business strategy and decision-making with specialized AI agents. Get insights on market analysis and strategic planning.",
        type: "website",
      },
    },
  },
  "content-and-video": {
    slug: "content-and-video",
    name: "Content and Video",
    displayName: "Content and Video",
    metadata: {
      title: "Content and Video AI Agents | Telex",
      description:
        "Create engaging content and videos with specialized AI agents. Automate content creation, video editing, social media posts, blog writing, and multimedia production.",
      keywords:
        "content creation AI, video production, content automation, social media content, blog writing, multimedia agents",
      openGraph: {
        title: "Content and Video AI Agents | Telex",
        description:
          "Create engaging content and videos with specialized AI agents. Automate content creation and multimedia production.",
        type: "website",
      },
    },
  },
};

export function getCategoryConfig(slug: string): CategoryConfig | null {
  return categoryConfigs[slug] || null;
}

export function getAllCategorySlugs(): string[] {
  return Object.keys(categoryConfigs);
}
