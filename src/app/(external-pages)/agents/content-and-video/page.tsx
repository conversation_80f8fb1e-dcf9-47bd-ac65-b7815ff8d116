import React from "react";
import { Metadata } from "next";
import CategoryPageClient from "../components/category-page-client";

// Metadata for SEO
export const metadata: Metadata = {
  title: "Content and Video AI Agents | Telex",
  description:
    "Create engaging content and videos with specialized AI agents. Automate content creation, video editing, social media posts, blog writing, and multimedia production.",
  keywords:
    "content creation AI, video production, content automation, social media content, blog writing, multimedia agents",
  openGraph: {
    title: "Content and Video AI Agents | Telex",
    description:
      "Create engaging content and videos with specialized AI agents. Automate content creation and multimedia production.",
    type: "website",
  },
};

export default function ContentAndVideoAgents() {
  return (
    <div className="bg-white relative">
      <CategoryPageClient 
        category="Content and Video" 
        categoryDisplayName="Content and Video"
      />
    </div>
  );
}
