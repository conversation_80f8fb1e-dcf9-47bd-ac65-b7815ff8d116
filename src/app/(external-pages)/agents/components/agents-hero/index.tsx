"use client";
import React from "react";
import Image from "next/image";

const AgentsHero = ({
  agentsSearch,
  setAgentsSearch,
  handleAgentsSearch,
  categoryTitle,
}: {
  agentsSearch: string;
  setAgentsSearch: React.Dispatch<React.SetStateAction<string>>;
  handleAgentsSearch: any;
  categoryTitle?: string;
}) => {
  //

  return (
    <div className="flex items-center justify-center">
      <div
        style={{ backgroundImage: "url('/images/agents-hero-bg.jpg')" }}
        className="bg-cover pt-[40px] sm:pt-[55px] bg-center bg-no-repeat h-[400px] md:h-[500px] lg:h-[600px] flex items-center justify-center text-center p-2 sm:p-6 xl:max-w-[1550px]"
      >
        <div className="bg-[#fff] rounded-md border border-[#98A2B3] h-full w-full flex items-center justify-center px-2 md:px-6 lg:px-48 ">
          <div className="flex flex-col items-center gap-6">
            <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold">
              {categoryTitle ? (
                <>
                  <span className="text-[#5F5FE1]">{categoryTitle}</span> AI Agents
                </>
              ) : (
                <>
                  <span className="text-[#5F5FE1]">Meet The AI Agents</span> That
                  Work With You To Grow Your Business
                </>
              )}
            </h1>
            <p className="w-full md:w-[60%] text-center text-sm">
              {categoryTitle ? (
                `Discover specialized ${categoryTitle.toLowerCase()} agents ready to help you grow to the next level. They bring intelligence and experience to streamline your business operations.`
              ) : (
                "1000s of agents are ready to help you grow to the next level. They bring in intelligence and experience across all the areas of your business to grow it."
              )}
            </p>
            <form
              className="flex gap-3 border border-[#E6EAEF] w-[90%] md:w-[60%] m-auto rounded-md h-10 px-2 bg-white"
              onSubmit={handleAgentsSearch}
            >
              <Image
                src={"/images/search-icon.svg"}
                alt="Search Icon"
                width={20}
                height={20}
              />
              <input
                type="text"
                placeholder="Find an Agent"
                className="outline-none border-none rounded-md w-full text-sm"
                value={agentsSearch}
                onChange={(e) => setAgentsSearch(e.target.value)}
              />
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentsHero;
