import React from "react";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

interface BreadcrumbProps {
  categoryName: string;
}

export default function Breadcrumb({ categoryName }: BreadcrumbProps) {
  return (
    <div className="flex items-center justify-center bg-gray-50">
      <div className="w-full px-2 md:px-6 lg:px-8 xl:max-w-[1550px]">
        <div className="flex items-center justify-between py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link
              href="/agents"
              className="hover:text-blue-600 transition-colors"
            >
              Agents
            </Link>
            <ChevronRight className="w-4 h-4" />
            <span className="text-gray-900 font-medium">{categoryName}</span>
          </nav>
          <Link
            href="/agents"
            className="text-sm text-blue-600 hover:text-blue-800 transition-colors font-medium"
          >
            ← Back to All Agents
          </Link>
        </div>
      </div>
    </div>
  );
}
