import React from "react";
import { Metadata } from "next";
import CategoryPageClient from "../components/category-page-client";

// Metadata for SEO
export const metadata: Metadata = {
  title: "Engineering DevOps AI Agents | Telex",
  description:
    "Streamline your development and operations with specialized AI agents. Automate deployments, monitor infrastructure, manage CI/CD pipelines, and optimize system performance.",
  keywords:
    "DevOps AI agents, infrastructure monitoring, CI/CD automation, deployment automation, system monitoring, engineering tools",
  openGraph: {
    title: "Engineering DevOps AI Agents | Telex",
    description:
      "Streamline your development and operations with specialized AI agents. Automate deployments and monitor infrastructure.",
    type: "website",
  },
};

export default function EngineeringDevOpsAgents() {
  return (
    <div className="bg-white relative">
      <CategoryPageClient 
        category="Engineering DevOps" 
        categoryDisplayName="Engineering DevOps"
      />
    </div>
  );
}
