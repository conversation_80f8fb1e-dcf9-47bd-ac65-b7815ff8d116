import React from "react";
import { Metadata } from "next";
import CategoryPageClient from "../components/category-page-client";

// Metadata for SEO
export const metadata: Metadata = {
  title: "Sales and Marketing AI Agents | Telex",
  description:
    "Boost your sales and marketing efforts with specialized AI agents. Automate lead generation, customer outreach, campaign management, and sales analytics.",
  keywords:
    "sales AI agents, marketing automation, lead generation, sales analytics, campaign management, CRM automation",
  openGraph: {
    title: "Sales and Marketing AI Agents | Telex",
    description:
      "Boost your sales and marketing efforts with specialized AI agents. Automate lead generation and campaign management.",
    type: "website",
  },
};

export default function SalesAndMarketingAgents() {
  return (
    <div className="bg-white relative">
      <CategoryPageClient 
        category="Sales and Marketing" 
        categoryDisplayName="Sales and Marketing"
      />
    </div>
  );
}
