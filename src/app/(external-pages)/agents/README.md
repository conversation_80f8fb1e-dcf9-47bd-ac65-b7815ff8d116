# Agents Pages Structure

This directory contains the AI Agents marketplace pages with individual category pages.

## Structure

```
agents/
├── page.tsx                           # Main agents page (shows all agents)
├── components/
│   ├── agents-page-client.tsx         # Main page client component
│   ├── category-page-client.tsx       # Shared component for category pages
│   ├── breadcrumb.tsx                 # Breadcrumb navigation component
│   └── ...                           # Other shared components
├── customer-support/
│   └── page.tsx                      # Customer Support agents page
├── sales-and-marketing/
│   └── page.tsx                      # Sales and Marketing agents page
├── engineering-devops/
│   └── page.tsx                      # Engineering DevOps agents page
├── documents-and-analysis/
│   └── page.tsx                      # Documents and Analysis agents page
├── strategy-business/
│   └── page.tsx                      # Strategy Business agents page
└── content-and-video/
    └── page.tsx                      # Content and Video agents page
```

## Categories

The system supports the following agent categories:

### New Categories (with dedicated pages)
- **Customer Support** (`/agents/customer-support`)
- **Sales and Marketing** (`/agents/sales-and-marketing`)
- **Engineering DevOps** (`/agents/engineering-devops`)
- **Documents and Analysis** (`/agents/documents-and-analysis`)
- **Strategy Business** (`/agents/strategy-business`)
- **Content and Video** (`/agents/content-and-video`)

### Legacy Categories (still supported)
- Application Performance Monitoring
- Cloud Monitoring
- Website Testing
- Webhook Testing
- Social Media

## Features

### Main Agents Page (`/agents`)
- Shows all agents from all categories
- Horizontal carousel for category filtering
- "All" button filters locally
- Category buttons link to dedicated category pages
- Search functionality across all agents

### Category Pages (`/agents/{category-slug}`)
- Shows only agents from the specific category
- Breadcrumb navigation with "Back to All Agents" link
- Category-specific hero section with tailored messaging
- Search functionality within the category
- Same agent grid layout as main page

### Components

#### CategoryPageClient
- Shared component used by all category pages
- Filters agents by category automatically
- Supports search within category
- Uses same caching mechanism as main page

#### Breadcrumb
- Shows navigation path: Agents > Category Name
- Includes "Back to All Agents" link
- Styled with gray background for visual separation

## URL Structure

- Main page: `/agents`
- Category pages: `/agents/{category-slug}`
  - `/agents/customer-support`
  - `/agents/sales-and-marketing`
  - `/agents/engineering-devops`
  - `/agents/documents-and-analysis`
  - `/agents/strategy-business`
  - `/agents/content-and-video`

## SEO

Each category page has its own:
- Custom page title
- Meta description
- Keywords
- Open Graph tags

## Data Flow

1. All pages use the same API endpoint (`/api/v1/agents`)
2. Data is cached globally for 5 minutes
3. Category pages filter the cached data by category
4. Search functionality works within the filtered results
5. Color coding is consistent across all pages
